# Rancangan Schema Database untuk Sistem Tagging Produk Finansial

## Struktur Data yang Dianalisis

```json
{
  "categoryLayer": "2",
  "categorySlug": "instrument-type", 
  "categoryLabel": "Instrument Type",
  "tagSlug": "stock",
  "tagLabel": "Stock"
}
```

## Model Database yang Dirancang

### 1. Model Category
Menyimpan hierarki kategori dengan sistem layer:

```prisma
model Category {
  id          String   @id @default(uuid(7)) @db.Uuid
  slug        String   @unique
  label       String
  layer       String   // "1", "2", "3a", "3b", "4"
  description String?
  isActive    Boolean  @default(true)
  sortOrder   Int?     // untuk pengurutan dalam layer yang sama
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relasi dengan tags
  tags Tag[]

  @@index([layer, sortOrder])
  @@index([slug])
  @@map("category")
}
```

### 2. Model Tag  
Menyimpan tag individual yang terikat pada kategori:

```prisma
model Tag {
  id          String   @id @default(uuid(7)) @db.Uuid
  slug        String   @unique
  label       String
  description String?
  isActive    Boolean  @default(true)
  categoryId  String   @map("category_id") @db.Uuid
  sortOrder   Int?     // untuk pengurutan dalam kategori yang sama
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relasi
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  productTags ProductTag[]

  @@index([categoryId])
  @@index([slug])
  @@map("tag")
}
```

### 3. Model ProductTag
Junction table untuk relasi many-to-many antara Product dan Tag:

```prisma
model ProductTag {
  id        String   @id @default(uuid(7)) @db.Uuid
  productId String   @map("product_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")

  // Relasi
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([productId, tagId])
  @@index([productId])
  @@index([tagId])
  @@map("product_tag")
}
```

### 4. Update Model Product
Tambahkan relasi ke ProductTag:

```prisma
model Product {
  // ... field yang sudah ada ...
  
  supportedNetworks supported_network[]
  productTags       ProductTag[]  // tambahan ini

  @@map("product")
}
```

## Diagram Entity Relationship

```mermaid
erDiagram
    Category ||--o{ Tag : has
    Product ||--o{ ProductTag : has
    Tag ||--o{ ProductTag : belongs_to
    Product ||--o{ supported_network : has

    Category {
        uuid id PK
        string slug UK
        string label
        string layer
        string description
        boolean isActive
        int sortOrder
        datetime createdAt
        datetime updatedAt
    }

    Tag {
        uuid id PK
        string slug UK
        string label
        string description
        boolean isActive
        uuid categoryId FK
        int sortOrder
        datetime createdAt
        datetime updatedAt
    }

    ProductTag {
        uuid id PK
        uuid productId FK
        uuid tagId FK
        datetime createdAt
    }

    Product {
        uuid id PK
        string displayName
        string tokenName
        string underlyingName
        string symbol
        string img
        string description
        decimal price
        decimal high
        decimal low
        decimal priceChange24h
        decimal priceChangePct24h
        decimal apy
        decimal tvl
        datetime createdAt
        datetime updatedAt
    }
```

## Keuntungan Rancangan Ini

### 1. **Skalabilitas**
- Mudah menambah kategori baru tanpa mengubah struktur
- Sistem layer yang fleksibel (bisa 3a, 3b, dst)

### 2. **Normalisasi Data**
- Tidak ada duplikasi data kategori/tag
- Relasi yang jelas antar entitas

### 3. **Performa Query**
- Index yang optimal untuk filtering dan pencarian
- Unique constraint mencegah duplikasi tag pada produk

### 4. **Maintainability**
- Struktur yang bersih dan mudah dipahami
- Soft delete dengan flag `isActive`

## Contoh Data Seed

```typescript
// Categories
const categories = [
  { slug: "asset-class", label: "Asset Class", layer: "1" },
  { slug: "instrument-type", label: "Instrument Type", layer: "2" },
  { slug: "sector-industry", label: "Sector/Industry", layer: "3a" },
  { slug: "type-factor-risk-profile", label: "Type/Factor/Risk Profile", layer: "3b" },
  { slug: "region-market-exposure", label: "Region / Market Exposure", layer: "4" }
];

// Tags
const tags = [
  { slug: "equities", label: "Equities", categorySlug: "asset-class" },
  { slug: "stock", label: "Stock", categorySlug: "instrument-type" },
  { slug: "technology", label: "Technology", categorySlug: "sector-industry" },
  { slug: "value", label: "Value", categorySlug: "type-factor-risk-profile" },
  { slug: "large-cap", label: "Large Cap", categorySlug: "type-factor-risk-profile" },
  { slug: "china", label: "China", categorySlug: "region-market-exposure" }
];
```

## Query Patterns Umum

### 1. Mendapatkan semua tag produk
```sql
SELECT p.*, c.label as category_label, c.layer, t.label as tag_label
FROM product p
JOIN product_tag pt ON p.id = pt.product_id
JOIN tag t ON pt.tag_id = t.id
JOIN category c ON t.category_id = c.id
WHERE p.id = $1;
```

### 2. Filter produk berdasarkan tag
```sql
SELECT DISTINCT p.*
FROM product p
JOIN product_tag pt ON p.id = pt.product_id
JOIN tag t ON pt.tag_id = t.id
WHERE t.slug IN ('technology', 'large-cap', 'china');
```

### 3. Grup tag berdasarkan kategori
```sql
SELECT c.layer, c.label as category_label, 
       array_agg(t.label) as tags
FROM category c
JOIN tag t ON c.id = t.category_id
WHERE c.is_active = true AND t.is_active = true
GROUP BY c.id, c.layer, c.label
ORDER BY c.layer;
```

## Rekomendasi Implementasi

1. **Buat migration terpisah** untuk setiap model
2. **Gunakan transaction** saat membuat product dengan tag
3. **Implementasikan caching** untuk kategori dan tag (jarang berubah)
4. **Buat service layer** untuk business logic tagging
5. **Tambahkan validation** untuk layer hierarchy consistency