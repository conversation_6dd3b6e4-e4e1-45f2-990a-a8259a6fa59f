import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ProductResponseDto } from './dto/product-response.dto';
import { BaseResponseDto, ResponseStatus } from '../backoffice/nav-management/dto/base-response.dto';

@Injectable()
export class ProductsService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(): Promise<BaseResponseDto<ProductResponseDto[]>> {
    try {
      const products = await this.prisma.product.findMany({
        include: {
          productTags: {
            include: {
              tag: {
                include: {
                  category: true,
                },
              },
            },
            orderBy: {
              id: 'asc',
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (!products || products.length === 0) {
        throw new NotFoundException('No products found');
      }

      const productResponses = products.map(this.transformProductToResponse);

      return BaseResponseDto.success(
        productResponses,
        'Products retrieved successfully',
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('ProductsService Error:', error);
      throw new InternalServerErrorException(
        'An error occurred while retrieving products',
      );
    }
  }

  private transformProductToResponse(product: any): ProductResponseDto {
    const tags = product.productTags.map((productTag: any) => {
      const tag = productTag.tag;
      const category = tag.category;
      return {
        categoryLayer: category.layer,
        categorySlug: category.slug,
        categoryLabel: category.label,
        tagSlug: tag.slug,
        tagLabel: tag.label,
      };
    });

    return {
      id: product.id,
      displayName: product.displayName,
      tokenName: product.tokenName,
      underlyingName: product.underlyingName,
      symbol: product.symbol,
      img: product.img || 'https://orbitum.com/img/orbitum.png',
      price: Number(product.price),
      high: Number(product.high || product.price),
      low: Number(product.low || product.price),
      priceChange24h: Number(product.priceChange24h),
      priceChangePct24h: Number(product.priceChangePct24h),
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
      tags,
    };
  }
}
